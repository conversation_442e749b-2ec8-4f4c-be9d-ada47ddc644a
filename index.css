/* CSS Variables - Enhanced Modern Design System */
:root {
    /* Primary Colors - Modern Education Theme with Enhanced Gradients */
    --primary-color: #6366f1;
    --primary-dark: #4338ca;
    --primary-light: #8b5cf6;
    --primary-ultra-light: #c7d2fe;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-gradient-modern: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    --primary-gradient-soft: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);

    /* Secondary Colors - Enhanced Palette */
    --secondary-color: #06b6d4;
    --secondary-dark: #0891b2;
    --secondary-light: #67e8f9;
    --secondary-ultra-light: #cffafe;
    --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);

    /* Accent Colors - Vibrant and Modern */
    --accent-color: #f59e0b;
    --accent-light: #fbbf24;
    --accent-dark: #d97706;
    --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);

    /* Success & Status Colors */
    --success-color: #10b981;
    --success-light: #6ee7b7;
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --warning-color: #f59e0b;
    --warning-light: #fbbf24;
    --error-color: #ef4444;
    --error-light: #fca5a5;
    --info-color: #3b82f6;
    --info-light: #93c5fd;

    /* Neutral Colors - Enhanced Contrast */
    --text-color: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --bg-color: #f9fafb;
    --bg-light: #ffffff;
    --bg-dark: #f3f4f6;
    --bg-section: #fefefe;
    
    /* Enhanced Shadows - Modern Depth System */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.15);
    --shadow-2xl: 0 24px 96px rgba(0, 0, 0, 0.2);
    --shadow-colored: 0 8px 32px rgba(99, 102, 241, 0.15);
    --shadow-colored-hover: 0 16px 48px rgba(99, 102, 241, 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

    /* Enhanced Transitions - Smooth Animations */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-elastic: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Modern Border Radius System */
    --radius-xs: 2px;
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 18px;
    --radius-xl: 24px;
    --radius-2xl: 32px;
    --radius-3xl: 48px;
    --radius-round: 50%;
    --radius-pill: 9999px;

    /* Enhanced Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;
    --spacing-5xl: 8rem;

    /* Modern Typography Scale */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-md: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    --font-3xl: 1.875rem;
    --font-4xl: 2.25rem;
    --font-5xl: 3rem;
    --font-6xl: 3.75rem;
    
    /* Z-index layers */
    --z-negative: -1;
    --z-normal: 1;
    --z-dropdown: 10;
    --z-sticky: 100;
    --z-fixed: 1000;
    --z-modal: 2000;
    --z-popover: 5000;
    --z-tooltip: 9000;
    --z-toast: 9500;
    --z-max: 10000;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
    font-size: 16px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-normal);
}

a:hover {
    color: var(--primary-dark);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

section {
    padding: var(--spacing-xl) 0;
    position: relative;
}

h1, h2, h3, h4 {
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    line-height: 1.3;
}

h1 {
    font-size: var(--font-3xl);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    position: relative;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.section-header h2:after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-top: 1rem;
}

h2 {
    text-align: center;
    font-size: var(--font-2xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
}

h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: var(--radius-sm);
}

h3 {
    font-size: var(--font-xl);
}

h4 {
    font-size: var(--font-lg);
}

p {
    margin-bottom: var(--spacing-md);
}

button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: inherit;
}

input, textarea, select {
    font-family: inherit;
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-light);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-max);
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.loader svg {
    width: 80px;
    height: 80px;
    transform: rotate(-90deg);
}

#loader-circle {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 200;
    stroke-dashoffset: 200;
    animation: loader-animation 2s ease infinite;
}

@keyframes loader-animation {
    0% {
        stroke-dashoffset: 200;
    }
    50% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: -200;
    }
}

.loader-name {
    margin-top: var(--spacing-md);
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--primary-color);
    opacity: 0;
    animation: fade-in 0.5s ease forwards;
    animation-delay: 0.5s;
}

/* Scroll Progress Indicator */
.scroll-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.05);
    z-index: var(--z-fixed);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.scroll-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    box-shadow: 0 0 10px rgba(79, 70, 229, 0.5);
}

.scroll-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    animation: shimmer 2s infinite;
}

body.dark-mode .scroll-progress-container {
    background: rgba(255, 255, 255, 0.05);
}

body.dark-mode .scroll-progress-bar {
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.6);
}

/* Back to Top Button */
#back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: #fff;
    border-radius: var(--radius-round);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: var(--z-fixed);
    box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
}

#back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#back-to-top:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-8px) scale(1.1);
    box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
}

#back-to-top i {
    font-size: var(--font-lg);
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    padding: var(--spacing-md) 0;
    transition: all var(--transition-normal);
    border-bottom: 1px solid rgba(79, 70, 229, 0.1);
}

header.scrolled {
    padding: var(--spacing-sm) 0;
    box-shadow: var(--shadow-md);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    flex: 0 0 auto;
}

.logo h1 {
    font-size: 1.8rem;
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all var(--transition-normal);
    font-weight: 800;
    letter-spacing: -1px;
}

nav {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    padding: 0;
    margin: 0;
}

.nav-link {
    color: var(--text-color);
    font-weight: 500;
    font-size: var(--font-md);
    padding: var(--spacing-sm) var(--spacing-md);
    position: relative;
    transition: all var(--transition-normal);
    border-radius: var(--radius-md);
    text-decoration: none;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transform: translateX(-50%);
    transition: width var(--transition-normal);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link:hover::before {
    width: 70%;
}

.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::before {
    width: 70%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Theme Toggle Button */
#theme-toggle-btn {
    width: 42px;
    height: 42px;
    border-radius: var(--radius-md);
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--primary-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

#theme-toggle-btn:hover {
    background-color: var(--primary-color);
}

#theme-toggle-btn i {
    font-size: var(--font-lg);
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

#theme-toggle-btn:hover i {
    color: #fff;
    transform: rotate(360deg);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
}

#mobile-menu-btn {
    width: 45px;
    height: 45px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(79, 70, 229, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.2);
}

#mobile-menu-btn:hover {
    background: rgba(79, 70, 229, 0.15);
    transform: scale(1.05);
}

#mobile-menu-btn span {
    display: block;
    width: 22px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#mobile-menu-btn.active {
    background: rgba(79, 70, 229, 0.2);
}

#mobile-menu-btn.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
}

#mobile-menu-btn.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

#mobile-menu-btn.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
}

/* Hero Section - Enhanced Modern Design */
.hero {
    background: var(--primary-gradient-modern);
    color: #fff;
    padding: var(--spacing-4xl) 0;
    position: relative;
    overflow: hidden;
    min-height: 95vh;
    display: flex;
    align-items: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    z-index: var(--z-negative);
}

.hero::after {
    content: '';
    position: absolute;
    top: -30%;
    right: -15%;
    width: 50%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.08) 0%, transparent 60%);
    transform: rotate(12deg);
    z-index: var(--z-negative);
    border-radius: var(--radius-3xl);
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xl);
    position: relative;
    z-index: var(--z-normal);
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.floating-image {
    animation: float 6s ease-in-out infinite;
    position: relative;
}

.image-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
    border-radius: 50px;
    z-index: -1;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

.hero-image img {
    width: 320px;
    height: 320px;
    object-fit: cover;
    border-radius: 30px;
    border: 8px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
    z-index: var(--z-normal);
    transition: transform 0.3s ease;
}

.hero-image img:hover {
    transform: scale(1.05) rotate(2deg);
}

.hero-shape-1,
.hero-shape-2 {
    position: absolute;
    border-radius: var(--radius-round);
    background: rgba(255, 255, 255, 0.1);
    z-index: var(--z-negative);
}

.hero-shape-1 {
    width: 200px;
    height: 200px;
    top: -30px;
    right: 20px;
    animation: pulse 8s ease-in-out infinite;
}

.hero-shape-2 {
    width: 150px;
    height: 150px;
    bottom: -20px;
    left: 30px;
    animation: pulse 6s ease-in-out infinite 1s;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.2;
    }
}

.hero h1 {
    font-size: 4rem;
    margin-bottom: var(--spacing-sm);
    position: relative;
    font-weight: 800;
    letter-spacing: -2px;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero .subtitle {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
    letter-spacing: 1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero p {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-lg);
    color: rgba(255, 255, 255, 0.85);
    line-height: 1.8;
    max-width: 600px;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.primary-btn,
.secondary-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-pill);
    font-weight: 600;
    font-size: var(--font-lg);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    z-index: var(--z-normal);
    text-decoration: none;
    border: none;
    cursor: pointer;
    min-height: 56px;
}

.primary-btn {
    background: var(--secondary-gradient);
    color: #fff;
    box-shadow: var(--shadow-colored);
    position: relative;
    overflow: hidden;
}

.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
    transition: left var(--transition-slow);
}

.primary-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.primary-btn:hover::before {
    left: 100%;
}

.primary-btn:hover::after {
    opacity: 1;
}

.primary-btn:hover {
    background: var(--secondary-gradient);
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-colored-hover);
    color: #fff;
}

.secondary-btn {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.secondary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.secondary-btn:hover::before {
    left: 100%;
}

.secondary-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-4px) scale(1.02);
    color: #fff;
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

.hero-buttons a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.hero-buttons i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.hero-buttons a:hover i {
    transform: scale(1.2);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
    justify-content: flex-start;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    min-width: 80px;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
}

/* Animation classes for hero section */
.animate-on-load {
    opacity: 0;
    transform: translateY(20px);
}

.animate-on-load.animated {
    animation: fadeInUp 0.8s forwards;
}

.hero-content .animate-on-load:nth-child(1) { animation-delay: 0.1s; }
.hero-content .animate-on-load:nth-child(2) { animation-delay: 0.3s; }
.hero-content .animate-on-load:nth-child(3) { animation-delay: 0.5s; }
.hero-content .animate-on-load:nth-child(4) { animation-delay: 0.7s; }
.hero-content .animate-on-load:nth-child(5) { animation-delay: 0.9s; }
.hero-image.animate-on-load { animation-delay: 0.5s; }

/* Features Section */
.features {
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.features::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background-color: rgba(37, 99, 235, 0.03);
    border-radius: var(--radius-round);
    z-index: var(--z-negative);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: var(--spacing-xl);
    perspective: 1000px;
}

.feature {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-bounce);
    background: linear-gradient(145deg, var(--bg-light), var(--bg-section));
    border: 2px solid rgba(99, 102, 241, 0.08);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(40px);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.feature.animate {
    animation: fadeInUp 0.8s var(--transition-bounce) forwards;
}

.feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--primary-gradient-soft);
    transition: height var(--transition-slow);
    z-index: var(--z-negative);
    border-radius: var(--radius-2xl);
}

.feature::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: var(--z-negative);
}

.feature:hover {
    transform: translateY(-20px) scale(1.03);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-light);
}

.feature:hover::before {
    height: 100%;
}

.feature:hover::after {
    opacity: 1;
}

.feature i {
    font-size: 4rem;
    background: var(--primary-gradient-modern);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-2xl);
    position: relative;
    z-index: var(--z-normal);
    transition: all var(--transition-bounce);
    display: inline-block;
    padding: var(--spacing-lg);
    border-radius: var(--radius-round);
    background-color: rgba(99, 102, 241, 0.1);
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-2xl);
}

.feature:hover i {
    transform: scale(1.15) rotate(8deg);
    filter: drop-shadow(0 8px 25px rgba(99, 102, 241, 0.4));
    background-color: rgba(99, 102, 241, 0.15);
    box-shadow: var(--shadow-glow);
}

.feature h3 {
    font-size: var(--font-lg);
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
    position: relative;
    z-index: var(--z-normal);
}

.feature p {
    color: var(--text-light);
    position: relative;
    z-index: var(--z-normal);
    line-height: 1.7;
}

/* Statistics Section */
.statistics {
    background-color: var(--primary-color);
    color: #fff;
    position: relative;
    overflow: hidden;
}

.statistics::before,
.statistics::after {
    content: '';
    position: absolute;
    border-radius: var(--radius-round);
    background-color: rgba(255, 255, 255, 0.05);
    z-index: var(--z-negative);
}

.statistics::before {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
}

.statistics::after {
    width: 200px;
    height: 200px;
    bottom: -100px;
    right: -100px;
}

.statistics h2 {
    color: #fff;
}

.statistics h2:after {
    background-color: rgba(255, 255, 255, 0.5);
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.statistic-item {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(5px);
    transition: all var(--transition-normal);
}

.statistic-item:hover {
    transform: translateY(-10px);
    background-color: rgba(255, 255, 255, 0.15);
}

.statistic-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    color: rgba(255, 255, 255, 0.9);
}

.statistic-number {
    font-size: var(--font-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.statistic-label {
    font-size: var(--font-md);
    color: rgba(255, 255, 255, 0.9);
}

/* Testimonials Section */
.testimonials {
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background-color: rgba(37, 99, 235, 0.03);
    border-radius: var(--radius-round);
    z-index: var(--z-negative);
}

.testimonials-slider {
    display: flex;
    overflow: hidden;
    margin-top: var(--spacing-xl);
}

.testimonial-item {
    flex: 0 0 100%;
    min-width: 100%;
    padding: var(--spacing-lg);
    background-color: var(--bg-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    margin: 0 var(--spacing-md);
}

.testimonial-content {
    margin-bottom: var(--spacing-lg);
}

.testimonial-rating {
    margin-bottom: var(--spacing-md);
    color: #ffc107;
}

.testimonial-rating i {
    margin-right: 2px;
}

.testimonial-content p {
    font-style: italic;
    color: var(--text-color);
    line-height: 1.7;
    position: relative;
    padding-left: var(--spacing-lg);
}

.testimonial-content p::before {
    content: '"';
    font-size: 4rem;
    color: var(--primary-light);
    position: absolute;
    top: -30px;
    left: 0;
    opacity: 0.3;
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-round);
    overflow: hidden;
    margin-right: var(--spacing-md);
    border: 3px solid var(--primary-light);
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info h4 {
    margin-bottom: 5px;
    color: var(--text-color);
}

.testimonial-info p {
    color: var(--text-light);
    margin: 0;
}

.testimonial-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: var(--spacing-lg);
    gap: var(--spacing-lg);
}

#testimonial-prev,
#testimonial-next {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-round);
    background-color: var(--bg-light);
    color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

#testimonial-prev:hover,
#testimonial-next:hover {
    background-color: var(--primary-color);
    color: #fff;
    transform: scale(1.1);
}

.testimonial-indicators {
    display: flex;
    gap: var(--spacing-sm);
}

.testimonial-indicator {
    width: 10px;
    height: 10px;
    border-radius: var(--radius-round);
    background-color: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.testimonial-indicator.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Download Section */
.download {
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.download::after {
    content: '';
    position: absolute;
    bottom: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background-color: rgba(37, 99, 235, 0.03);
    border-radius: var(--radius-round);
    z-index: var(--z-negative);
}

.app-info {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: 2.5rem;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.1);
    position: relative;
    overflow: hidden;
}

.app-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.05), transparent);
    animation: shimmer 3s infinite;
}

.version-info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.version-info p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin: 0;
    padding: 0.5rem 1.5rem;
    background: rgba(79, 70, 229, 0.05);
    border-radius: 25px;
    border: 1px solid rgba(79, 70, 229, 0.1);
    transition: all 0.3s ease;
}

.version-info p:hover {
    background: rgba(79, 70, 229, 0.1);
    transform: translateY(-2px);
}

.version-info .version,
.version-info .release-date {
    font-weight: 600;
    color: var(--primary-color);
}

.app-badges {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(16, 185, 129, 0.1));
    border-radius: 25px;
    color: var(--primary-color);
    border: 1px solid rgba(79, 70, 229, 0.2);
    transition: all 0.3s ease;
    font-weight: 500;
}

.badge:hover {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(16, 185, 129, 0.15));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.2);
}

.badge i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.download-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.download-option {
    text-align: center;
    padding: 3rem 2rem;
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(145deg, #ffffff, #f1f5f9);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    background-clip: padding-box;
}

.download-option::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 25px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.download-option:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 25px 60px rgba(79, 70, 229, 0.2);
}

.download-option:hover::before {
    opacity: 1;
}

.download-option i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    transition: transform var(--transition-normal);
}

.download-option:hover i {
    transform: scale(1.1);
}

.download-option h3 {
    font-size: var(--font-xl);
    margin-bottom: var(--spacing-sm);
}

.download-option p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
}

.download-btn {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: #fff;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: var(--z-normal);
    box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: width var(--transition-normal);
    z-index: var(--z-negative);
}

.download-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
    color: #fff;
}

.download-btn:hover::before {
    width: 100%;
}

.file-size {
    display: block;
    margin-top: var(--spacing-sm);
    font-size: var(--font-sm);
    color: var(--text-light);
}

.system-requirements {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.system-requirements h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #0066cc;
    margin-bottom: 1.5rem;
}

.system-requirements h3 i {
    font-size: 1.4rem;
}

.requirements-tabs .tab-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #eee;
    padding-bottom: 1rem;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: #666;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.tab-btn i {
    font-size: 1.2rem;
}

.tab-btn.active {
    background: #0066cc;
    color: white;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane ul li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.tab-pane ul li:last-child {
    border-bottom: none;
}

.tab-pane ul li i {
    color: #0066cc;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.tab-pane ul li strong {
    color: #444;
    margin-left: 0.5rem;
}

/* FAQ Section */
.faq {
    background-color: var(--bg-color);
    position: relative;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-item:hover {
    box-shadow: var(--shadow-md);
}

.faq-item h3 {
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-color);
    margin-bottom: 0;
    font-size: var(--font-lg);
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-normal);
}

.faq-item h3::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.faq-item h3.active {
    color: var(--primary-color);
}

.faq-item h3.active::after {
    transform: rotate(180deg);
}

.faq-item p {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    color: var(--text-light);
    margin: 0;
    line-height: 1.7;
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-item p.active {
    max-height: 1000px;
    padding-top: var(--spacing-md);
}

.faq-more {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.faq-more-btn {
    display: inline-block;
    color: var(--primary-color);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.faq-more-btn:hover {
    background-color: var(--primary-color);
    color: #fff;
    transform: translateY(-3px);
}

/* Contact Section */
.contact {
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background-color: rgba(37, 99, 235, 0.03);
    border-radius: var(--radius-round);
    z-index: var(--z-negative);
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-round);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary-color);
    font-size: var(--font-lg);
}

.contact-text h3 {
    font-size: var(--font-md);
    margin-bottom: var(--spacing-sm);
}

.contact-text p {
    color: var(--text-light);
    margin: 0;
}

.contact-social {
    margin-top: var(--spacing-md);
}

.contact-social h3 {
    font-size: var(--font-md);
    margin-bottom: var(--spacing-md);
}

.social-icons {
    display: flex;
    gap: var(--spacing-md);
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: var(--radius-round);
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: #fff;
    transform: translateY(-3px);
}

.contact-form-container {
    background-color: var(--bg-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.contact-form {
    display: grid;
    gap: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-md);
    font-family: inherit;
    transition: all var(--transition-normal);
    background-color: var(--bg-light);
    color: var(--text-color);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.submit-btn {
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    z-index: var(--z-normal);
    box-shadow: var(--shadow-sm);
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: width var(--transition-normal);
    z-index: var(--z-negative);
}

.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.submit-btn:hover::before {
    width: 100%;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: #fff;
    padding: var(--spacing-xl) 0 var(--spacing-md);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
    z-index: var(--z-normal);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo h3 {
    color: #fff;
    font-size: var(--font-xl);
    margin-bottom: var(--spacing-md);
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.7);
}

.footer-links h4,
.footer-newsletter h4 {
    color: #fff;
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.footer-links h4::after,
.footer-newsletter h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-light);
}

.footer-links ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: all var(--transition-normal);
}

.footer-links ul li a:hover {
    color: #fff;
    padding-right: var(--spacing-sm);
}

.footer-newsletter p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: var(--spacing-md);
}

.newsletter-form {
    display: flex;
    gap: var(--spacing-sm);
}

.newsletter-form input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.newsletter-form input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
}

.newsletter-form input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.newsletter-form button {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: var(--radius-md);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all var(--transition-normal);
}

.newsletter-form button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* Dark Mode Styles */
body.dark-mode {
    --text-color: #e2e8f0;
    --text-light: #94a3b8;
    --bg-color: #0f172a;
    --bg-light: #1e293b;
    --bg-dark: #020617;
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 5px 20px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.6);
    
    /* Dark mode specific colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    --secondary-color: #22d3ee;
    --secondary-dark: #06b6d4;
    --secondary-light: #67e8f9;
}

body.dark-mode header {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: rgba(99, 102, 241, 0.2);
}

body.dark-mode #theme-toggle-btn {
    border-color: var(--primary-color);
}

body.dark-mode #theme-toggle-btn i {
    color: var(--primary-color);
}

body.dark-mode #theme-toggle-btn:hover {
    background-color: var(--primary-color);
}

body.dark-mode #theme-toggle-btn:hover i {
    color: #fff;
}

body.dark-mode #mobile-menu-btn {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.3);
}

body.dark-mode #mobile-menu-btn:hover {
    background: rgba(99, 102, 241, 0.25);
}

body.dark-mode #mobile-menu-btn span {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

body.dark-mode nav {
    background: linear-gradient(145deg, var(--bg-light), var(--bg-dark));
    border-left-color: rgba(99, 102, 241, 0.2);
    box-shadow: -10px 0 40px rgba(0, 0, 0, 0.5);
}

body.dark-mode .nav-link {
    color: var(--text-color);
}

body.dark-mode .nav-link:hover,
body.dark-mode .nav-link.active {
    background: rgba(99, 102, 241, 0.2);
    color: var(--primary-light);
}

body.dark-mode .preloader {
    background-color: var(--bg-dark);
}

body.dark-mode .hero {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

body.dark-mode .hero::after {
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
}

body.dark-mode .feature {
    background: linear-gradient(145deg, var(--bg-light), var(--bg-color));
    border-color: rgba(99, 102, 241, 0.2);
}

body.dark-mode .feature::before {
    background: linear-gradient(to bottom, rgba(99, 102, 241, 0.1), transparent);
}

body.dark-mode .feature:hover {
    box-shadow: 0 20px 60px rgba(99, 102, 241, 0.3);
}

body.dark-mode .download-option {
    background: linear-gradient(145deg, var(--bg-light), var(--bg-color));
    border-color: rgba(99, 102, 241, 0.2);
}

body.dark-mode .download-option:hover {
    box-shadow: 0 25px 60px rgba(99, 102, 241, 0.3);
}

body.dark-mode .app-info,
body.dark-mode .system-requirements {
    background: linear-gradient(145deg, var(--bg-light), var(--bg-color));
    border-color: rgba(99, 102, 241, 0.2);
}

body.dark-mode .tab-content {
    background-color: var(--bg-light);
}

body.dark-mode .tab-btn {
    background-color: var(--bg-light);
    border-color: rgba(99, 102, 241, 0.2);
    color: var(--text-color);
}

body.dark-mode .tab-btn:hover:not(.active) {
    background-color: rgba(99, 102, 241, 0.1);
}

body.dark-mode .tab-btn.active {
    background: var(--primary-color);
    color: white;
}

body.dark-mode .contact-form-container {
    background-color: var(--bg-dark);
}

body.dark-mode .form-group input,
body.dark-mode .form-group textarea {
    background-color: var(--bg-light);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .form-group input:focus,
body.dark-mode .form-group textarea:focus {
    border-color: var(--primary-color);
    background-color: rgba(99, 102, 241, 0.1);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

body.dark-mode footer {
    background: linear-gradient(135deg, var(--bg-dark) 0%, #000000 100%);
}

body.dark-mode .stat-item {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.3);
}

body.dark-mode .stat-item:hover {
    background: rgba(99, 102, 241, 0.25);
}

body.dark-mode .version-info p {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
}

body.dark-mode .version-info p:hover {
    background: rgba(99, 102, 241, 0.2);
}

body.dark-mode .badge {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(34, 211, 238, 0.15));
    border-color: rgba(99, 102, 241, 0.3);
}

body.dark-mode .badge:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.25), rgba(34, 211, 238, 0.25));
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

/* Mobile menu overlay */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: calc(var(--z-dropdown) - 1);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Prevent body scrolling when menu is open */
body.menu-open {
    overflow: hidden;
}

/* Enhanced Interactive Elements */
.interactive-element {
    transition: all var(--transition-bounce);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
}

.interactive-element:active {
    transform: translateY(0) scale(0.98);
}

/* Magnetic Effect for Buttons */
.magnetic-btn {
    transition: all var(--transition-normal);
    position: relative;
}

.magnetic-btn:hover {
    transform: scale(1.05);
}

/* Parallax Elements */
.parallax-element {
    transition: transform var(--transition-slow);
}

/* Smooth Reveal Animation */
.reveal-element {
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--transition-slow);
}

.reveal-element.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Stagger Animation for Lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
}

.stagger-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.stagger-item:nth-child(1) { transition-delay: 0.1s; }
.stagger-item:nth-child(2) { transition-delay: 0.2s; }
.stagger-item:nth-child(3) { transition-delay: 0.3s; }
.stagger-item:nth-child(4) { transition-delay: 0.4s; }
.stagger-item:nth-child(5) { transition-delay: 0.5s; }
.stagger-item:nth-child(6) { transition-delay: 0.6s; }

/* Hover Glow Effect */
.glow-on-hover {
    transition: all var(--transition-normal);
}

.glow-on-hover:hover {
    box-shadow: var(--shadow-glow);
    animation: glow 2s ease-in-out infinite alternate;
}

/* Tilt Effect */
.tilt-element {
    transition: transform var(--transition-normal);
}

.tilt-element:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient-modern);
    color: white;
    border: none;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    transition: all var(--transition-bounce);
    z-index: var(--z-fixed);
}

.fab:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-2xl);
}

.fab:active {
    transform: scale(0.95);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    html {
        font-size: 15px;
    }
}

@media (max-width: 992px) {
    html {
        font-size: 14px;
    }
    
    section {
        padding: var(--spacing-lg) 0;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-xl) 0;
    }
    
    .contact-container {
        grid-template-columns: 1fr;
    }
    
    .contact-info {
        order: 2;
    }
    
    .contact-form-container {
        order: 1;
        margin-bottom: var(--spacing-lg);
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    nav {
        position: fixed;
        top: 0;
        right: -100%;
        width: 85%;
        max-width: 320px;
        height: 100vh;
        background: linear-gradient(145deg, var(--bg-light), var(--bg-color));
        backdrop-filter: blur(20px);
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.2);
        transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: var(--z-dropdown);
        padding: 2rem 0;
        overflow-y: auto;
        border-left: 1px solid rgba(79, 70, 229, 0.1);
    }
    
    nav.active {
        right: 0;
    }
    
    nav::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        opacity: 0.1;
    }
    
    .nav-menu {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        padding: 0 1.5rem;
        margin-top: 3rem;
    }
    
    .nav-link {
        display: block;
        width: 100%;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        text-decoration: none;
        color: var(--text-color);
        margin-bottom: 0.5rem;
        z-index: 2;
    }
    
    .nav-link::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 50%;
        width: 4px;
        height: 60%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        transform: translateY(-50%) scaleY(0);
        transition: transform 0.3s ease;
        border-radius: 2px;
        z-index: -1;
    }
    
    .nav-link:hover::before,
    .nav-link.active::before {
        transform: translateY(-50%) scaleY(1);
    }
    
    .nav-link:hover,
    .nav-link.active {
        background: rgba(79, 70, 229, 0.1);
        color: var(--primary-color);
        transform: translateX(8px);
    }
    
    .hero .container {
        flex-direction: column-reverse;
        text-align: center;
    }
    
    .hero-content {
        max-width: 100%;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-platforms {
        justify-content: center;
    }
    
    .hero-image {
        margin-bottom: var(--spacing-lg);
    }
    
    .hero-image img {
        width: 250px;
        height: 250px;
    }
}

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero .subtitle {
        font-size: 1.2rem;
    }
    
    .hero-stats {
        justify-content: center;
        gap: 1rem;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 0.75rem;
    }
    
    .stat-number {
        font-size: 1.4rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .hero-buttons a {
        width: 100%;
        max-width: 280px;
        justify-content: center;
        padding: 1rem 2rem;
    }
    
    .download-options {
        grid-template-columns: 1fr;
    }
    
    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .feature {
        padding: var(--spacing-lg);
    }
    
    .version-info {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .app-badges {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-buttons {
        flex-wrap: wrap;
    }
    
    .footer-content {
        gap: var(--spacing-lg);
    }
    
    .footer-links h4::after,
    .footer-newsletter h4::after {
        right: auto;
        left: 0;
    }
}

@media (max-width: 576px) {
    .container {
        width: 95%;
        padding: 0 var(--spacing-sm);
    }
    
    .statistics-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-links h4::after,
    .footer-newsletter h4::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .social-icons,
    .newsletter-form {
        justify-content: center;
    }
    
    #back-to-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-xl) 0;
    }
    
    .hero h1 {
        font-size: var(--font-2xl);
    }
    
    .hero .subtitle {
        font-size: var(--font-lg);
    }
    
    .hero-image img {
        width: 200px;
        height: 200px;
    }
}

/* Enhanced Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    25% {
        transform: translateY(-8px) rotate(1deg);
    }
    50% {
        transform: translateY(-15px) rotate(0deg);
    }
    75% {
        transform: translateY(-8px) rotate(-1deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.4;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.2;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        transform: rotate(-200deg);
        opacity: 0;
    }
    to {
        transform: rotate(0);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
