document.addEventListener('DOMContentLoaded', function() {
    // Preloader
    const preloader = document.querySelector('.preloader');
    
    // Hide preloader after page load
    window.addEventListener('load', function() {
        setTimeout(() => {
            preloader.classList.add('hidden');
            
            // Animate elements with 'animate-on-load' class
            document.querySelectorAll('.animate-on-load').forEach((element, index) => {
                setTimeout(() => {
                    element.classList.add('animated');
                }, 100 * index);
            });
        }, 500);
    });

    // Scroll Progress Indicator
    const scrollProgress = document.querySelector('.scroll-progress-bar');
    
    window.addEventListener('scroll', function() {
        const totalHeight = document.body.scrollHeight - window.innerHeight;
        const progress = (window.pageYOffset / totalHeight) * 100;
        scrollProgress.style.width = progress + '%';
        
        // Add 'scrolled' class to header when scrolling
        const header = document.querySelector('header');
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Show/hide back to top button
        const backToTopBtn = document.getElementById('back-to-top');
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    // Back to Top Button
    const backToTopBtn = document.getElementById('back-to-top');
    if (backToTopBtn) {
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Mobile Menu Toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const navMenu = document.querySelector('nav');
    
    // Create overlay element
    const menuOverlay = document.createElement('div');
    menuOverlay.className = 'menu-overlay';
    document.body.appendChild(menuOverlay);
    
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            this.classList.toggle('active');
            navMenu.classList.toggle('active');
            menuOverlay.classList.toggle('active');
            
            // Prevent body scrolling when menu is open
            document.body.classList.toggle('menu-open');
        });
        
        // Close menu when clicking overlay
        menuOverlay.addEventListener('click', function() {
            mobileMenuBtn.classList.remove('active');
            navMenu.classList.remove('active');
            menuOverlay.classList.remove('active');
            document.body.classList.remove('menu-open');
        });
        
        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenuBtn.classList.remove('active');
                navMenu.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.classList.remove('menu-open');
            });
        });
        
        // Close menu with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                mobileMenuBtn.classList.remove('active');
                navMenu.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.classList.remove('menu-open');
            }
        });
    }

    // Load app metadata from JSON file
    let appMetadata = {};

    fetch('metadata.json')
        .then(response => response.json())
        .then(data => {
            appMetadata = data;
            console.log('Metadata loaded successfully');

            // Update app name throughout the page
            updateAppName(appMetadata.appName);

            // Update app version
            updateAppVersion(appMetadata.version);

            // Update release date
            updateReleaseDate(appMetadata.releaseDate);

            // Update platform minimum versions
            updatePlatformVersions(appMetadata.platforms);

            // Update download buttons with file sizes
            updateDownloadInfo();

            // Update FAQ from metadata
            updateFAQ(appMetadata.faq);
            
            // Update features from metadata (if available)
            if (appMetadata.features && Array.isArray(appMetadata.features)) {
                updateFeatures(appMetadata.features);
            }
            
            // Update current year in footer
            updateCurrentYear();
        })
        .catch(error => {
            console.error('Error loading metadata:', error);
        });

    // Function to update app name throughout the page
    function updateAppName(appName) {
        const appNameElements = document.querySelectorAll('.logo h1, .footer-logo h3, .hero h1, .loader-name');
        appNameElements.forEach(element => {
            element.textContent = appName;
        });

        // Update page title
        document.title = `تحميل ${appName} | تطبيق إدارة المدرسين`;
    }

    // Function to update app version
    function updateAppVersion(version) {
        const versionElements = document.querySelectorAll('.version');
        versionElements.forEach(element => {
            element.textContent = version;
        });
    }

    // Function to update release date
    function updateReleaseDate(releaseDate) {
        const releaseDateElements = document.querySelectorAll('.release-date');
        releaseDateElements.forEach(element => {
            // Format date if needed
            const formattedDate = formatDate(releaseDate);
            element.textContent = formattedDate;
        });
    }
    
    // Function to update platform minimum versions
    function updatePlatformVersions(platforms) {
        if (platforms) {
            if (platforms.android) {
                const androidVersionElements = document.querySelectorAll('.android-version');
                androidVersionElements.forEach(element => {
                    element.textContent = platforms.android.minVersion;
                });
            }
            
            if (platforms.ios) {
                const iosVersionElements = document.querySelectorAll('.ios-version');
                iosVersionElements.forEach(element => {
                    element.textContent = platforms.ios.minVersion;
                });
            }
            
            if (platforms.windows) {
                const windowsVersionElements = document.querySelectorAll('.windows-version');
                windowsVersionElements.forEach(element => {
                    element.textContent = platforms.windows.minVersion;
                });
            }
        }
    }

    // Function to format date (YYYY-MM-DD to DD/MM/YYYY)
    function formatDate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        } catch (e) {
            console.error('Error formatting date:', e);
            return dateString;
        }
    }
    
    // Function to update current year in footer
    function updateCurrentYear() {
        const currentYearElement = document.getElementById('current-year');
        if (currentYearElement) {
            currentYearElement.textContent = new Date().getFullYear();
        }
    }

    // Function to update features from metadata
    function updateFeatures(featuresData) {
        const featuresGrid = document.querySelector('.features-grid');
        if (!featuresGrid) return;
        
        // Map feature names to icons
        const featureIcons = {
            'تسجيل الحضور والغياب': 'fas fa-user-check',
            'إدارة الفصول الدراسية': 'fas fa-chalkboard-teacher',
            'تقارير وإحصائيات': 'fas fa-chart-line',
            'جدولة المهام': 'fas fa-calendar-alt',
            'تتبع الدرجات': 'fas fa-graduation-cap',
            'واجهة سهلة الاستخدام': 'fas fa-mobile-alt'
        };
        
        // Check if features are already populated
        const existingFeatures = featuresGrid.querySelectorAll('.feature');
        if (existingFeatures.length > 0) {
            // Just add the animation class to existing features
            existingFeatures.forEach((feature, index) => {
                feature.style.animationDelay = `${index * 0.2}s`;
            });
        } else {
            // Create features dynamically
            featuresData.forEach((featureName, index) => {
                const featureElement = document.createElement('div');
                featureElement.className = 'feature';
                featureElement.style.animationDelay = `${index * 0.2}s`;
                
                const iconClass = featureIcons[featureName] || 'fas fa-star';
                
                featureElement.innerHTML = `
                    <i class="${iconClass}"></i>
                    <h3>${featureName}</h3>
                    <p>وصف مميز للميزة سيتم إضافته لاحقًا.</p>
                `;
                
                featuresGrid.appendChild(featureElement);
            });
        }
    }

    // Initialize testimonials slider
    function initTestimonialsSlider() {
        const testimonialsSlider = document.querySelector('.testimonials-slider');
        const testimonialItems = document.querySelectorAll('.testimonial-item');
        const prevBtn = document.getElementById('testimonial-prev');
        const nextBtn = document.getElementById('testimonial-next');
        const indicatorsContainer = document.getElementById('testimonial-indicators');
        
        if (!testimonialsSlider || testimonialItems.length === 0) return;
        
        let currentIndex = 0;
        const itemCount = testimonialItems.length;
        
        // Create indicators
        if (indicatorsContainer) {
            for (let i = 0; i < itemCount; i++) {
                const indicator = document.createElement('div');
                indicator.className = 'testimonial-indicator';
                if (i === 0) indicator.classList.add('active');
                indicator.dataset.index = i;
                indicatorsContainer.appendChild(indicator);
            }
        }
        
        // Update slider function
        function updateSlider() {
            // Only apply transform on mobile view
            if (window.innerWidth < 992) {
                testimonialsSlider.style.transform = `translateX(${currentIndex * -100}%)`;
            }
            
            // Update indicators
            document.querySelectorAll('.testimonial-indicator').forEach((indicator, index) => {
                if (index === currentIndex) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }
            });
        }
        
        // Previous button
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                if (currentIndex > 0) {
                    currentIndex--;
                } else {
                    currentIndex = itemCount - 1;
                }
                updateSlider();
            });
        }
        
        // Next button
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                if (currentIndex < itemCount - 1) {
                    currentIndex++;
                } else {
                    currentIndex = 0;
                }
                updateSlider();
            });
        }
        
        // Indicator clicks
        document.querySelectorAll('.testimonial-indicator').forEach(indicator => {
            indicator.addEventListener('click', function() {
                currentIndex = parseInt(this.dataset.index);
                updateSlider();
            });
        });
        
        // Touch swipe for mobile
        let touchStartX = 0;
        let touchEndX = 0;
        
        testimonialsSlider.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        testimonialsSlider.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
        
        function handleSwipe() {
            if (touchEndX < touchStartX) {
                // Swipe left (next)
                if (currentIndex < itemCount - 1) {
                    currentIndex++;
                } else {
                    currentIndex = 0;
                }
            } else if (touchEndX > touchStartX) {
                // Swipe right (previous)
                if (currentIndex > 0) {
                    currentIndex--;
                } else {
                    currentIndex = itemCount - 1;
                }
            }
            updateSlider();
        }
        
        // Auto slide every 5 seconds
        let autoSlideInterval = setInterval(function() {
            if (currentIndex < itemCount - 1) {
                currentIndex++;
            } else {
                currentIndex = 0;
            }
            updateSlider();
        }, 5000);
        
        // Stop auto slide on hover
        testimonialsSlider.addEventListener('mouseenter', function() {
            clearInterval(autoSlideInterval);
        });
        
        // Resume auto slide on mouse leave
        testimonialsSlider.addEventListener('mouseleave', function() {
            autoSlideInterval = setInterval(function() {
                if (currentIndex < itemCount - 1) {
                    currentIndex++;
                } else {
                    currentIndex = 0;
                }
                updateSlider();
            }, 5000);
        });
        
        // Initial update
        updateSlider();
        
        // Update on window resize
        window.addEventListener('resize', updateSlider);
    }

    // Animate statistics counters
    function animateStatisticCounters() {
        const statisticNumbers = document.querySelectorAll('.statistic-number');
        
        statisticNumbers.forEach(number => {
            const target = parseFloat(number.getAttribute('data-count'));
            const isDecimal = target % 1 !== 0;
            const duration = 2000; // Animation duration in milliseconds
            const frameDuration = 1000 / 60; // 60 fps
            const totalFrames = Math.round(duration / frameDuration);
            let frame = 0;
            let currentCount = 0;
            
            function updateCount() {
                frame++;
                const progress = frame / totalFrames;
                const easeOutQuad = 1 - Math.pow(1 - progress, 2); // Ease out function
                currentCount = isDecimal ? 
                    (target * easeOutQuad).toFixed(1) : 
                    Math.floor(target * easeOutQuad);
                
                number.textContent = currentCount;
                
                if (frame < totalFrames) {
                    requestAnimationFrame(updateCount);
                } else {
                    number.textContent = target;
                }
            }
            
            // Start animation when element is in viewport
            const observer = new IntersectionObserver(entries => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        requestAnimationFrame(updateCount);
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(number);
        });
    }

    // Function to update FAQ from metadata
    function updateFAQ(faqData) {
        if (!faqData || !Array.isArray(faqData) || faqData.length === 0) {
            return;
        }

        const faqContainer = document.getElementById('faq-container');
        if (!faqContainer) {
            return;
        }

        // Clear existing FAQ items
        faqContainer.innerHTML = '';

        // Add FAQ items from metadata
        faqData.forEach((item, index) => {
            const faqItem = document.createElement('div');
            faqItem.className = 'faq-item';

            const question = document.createElement('h3');
            question.textContent = item.question;
            question.style.cursor = 'pointer';

            const answer = document.createElement('p');
            answer.textContent = item.answer;
            
            // First FAQ item should be open by default
            if (index === 0) {
                question.classList.add('active');
                answer.classList.add('active');
            }

            faqItem.appendChild(question);
            faqItem.appendChild(answer);

            // Add click event
            question.addEventListener('click', function() {
                this.classList.toggle('active');
                answer.classList.toggle('active');
            });

            faqContainer.appendChild(faqItem);
        });
    }

    // System requirements tabs
    function initRequirementsTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        if (tabButtons.length === 0 || tabPanes.length === 0) return;
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to current button
                this.classList.add('active');
                
                // Hide all tab panes
                tabPanes.forEach(pane => pane.classList.remove('active'));
                
                // Show the target tab pane
                const targetId = this.getAttribute('data-target');
                document.getElementById(targetId).classList.add('active');
            });
        });
    }

    // Function to update download info
    function updateDownloadInfo() {
        if (appMetadata.platforms) {
            // Update Android info
            if (appMetadata.platforms.android) {
                const androidElement = document.querySelector('#android-download');
                if (androidElement) {
                    androidElement.setAttribute('data-size', appMetadata.platforms.android.fileSize);
                    
                    // Check if file size element already exists
                    if (!androidElement.querySelector('.file-size')) {
                        const sizeInfo = document.createElement('span');
                        sizeInfo.className = 'file-size';
                        sizeInfo.textContent = `(${appMetadata.platforms.android.fileSize})`;
                        androidElement.appendChild(sizeInfo);
                    }
                    
                    // Update href if available
                    if (appMetadata.platforms.android.downloadUrl) {
                        androidElement.href = appMetadata.platforms.android.downloadUrl;
                    }
                }
            }

            // Update iOS info
            if (appMetadata.platforms.ios) {
                const iosElement = document.querySelector('#ios-download');
                if (iosElement) {
                    iosElement.setAttribute('data-size', appMetadata.platforms.ios.fileSize);
                    
                    // Check if file size element already exists
                    if (!iosElement.querySelector('.file-size')) {
                        const sizeInfo = document.createElement('span');
                        sizeInfo.className = 'file-size';
                        sizeInfo.textContent = `(${appMetadata.platforms.ios.fileSize})`;
                        iosElement.appendChild(sizeInfo);
                    }
                    
                    // Update href if available
                    if (appMetadata.platforms.ios.downloadUrl) {
                        iosElement.href = appMetadata.platforms.ios.downloadUrl;
                    }
                }
            }

            // Update Windows info
            if (appMetadata.platforms.windows) {
                const windowsElement = document.querySelector('#windows-download');
                if (windowsElement) {
                    windowsElement.setAttribute('data-size', appMetadata.platforms.windows.fileSize);
                    
                    // Check if file size element already exists
                    if (!windowsElement.querySelector('.file-size')) {
                        const sizeInfo = document.createElement('span');
                        sizeInfo.className = 'file-size';
                        sizeInfo.textContent = `(${appMetadata.platforms.windows.fileSize})`;
                        windowsElement.appendChild(sizeInfo);
                    }
                    
                    // Update href if available
                    if (appMetadata.platforms.windows.downloadUrl) {
                        windowsElement.href = appMetadata.platforms.windows.downloadUrl;
                    }
                }
            }
        }
    }

    // Track download clicks
    const downloadButtons = document.querySelectorAll('.download-btn');
    downloadButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const platform = this.id ? this.id.split('-')[0] : 'general';
            console.log(`Download clicked for ${platform}`);

            // Get file size from metadata if available
            let fileSize = '';
            if (this.getAttribute('data-size')) {
                fileSize = ` (${this.getAttribute('data-size')})`;
            }

            // In a real application, you would send this data to your analytics service
            // Example: trackDownload(platform);

            // For demo purposes, we'll show an alert
            if (this.id) {
                e.preventDefault();
                
                // Create a toast notification instead of an alert
                createToast(`تم بدء تحميل التطبيق لنظام ${getPlatformName(platform)}${fileSize}. شكراً لاختيارك تطبيقنا!`, 'success');
                
                // Simulate download after a short delay
                setTimeout(() => {
                    if (this.getAttribute('href')) {
                        window.location.href = this.getAttribute('href');
                    }
                }, 1500);
            }
        });
    });

    // Toast notification system
    function createToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // Add icon based on type
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas fa-${icon}"></i>
            </div>
            <div class="toast-content">${message}</div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to container
        toastContainer.appendChild(toast);
        
        // Show toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // Close button
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 5000);
        
        return toast;
    }
    
    // Add CSS for toast notifications
    function addToastStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            .toast-container {
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 9999;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .toast {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                opacity: 0;
                transform: translateX(-20px);
                transition: all 0.3s ease;
                min-width: 300px;
                max-width: 450px;
            }
            
            .toast.show {
                opacity: 1;
                transform: translateX(0);
            }
            
            .toast-icon {
                font-size: 1.5rem;
                margin-right: 12px;
            }
            
            .toast-content {
                flex: 1;
            }
            
            .toast-close {
                background: none;
                border: none;
                font-size: 1rem;
                cursor: pointer;
                opacity: 0.5;
                transition: opacity 0.3s;
            }
            
            .toast-close:hover {
                opacity: 1;
            }
            
            .toast-info .toast-icon {
                color: #0066cc;
            }
            
            .toast-success .toast-icon {
                color: #28a745;
            }
            
            .toast-error .toast-icon {
                color: #dc3545;
            }
            
            .toast-warning .toast-icon {
                color: #ffc107;
            }
            
            body.dark-mode .toast {
                background-color: #333;
                color: #fff;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
        `;
        document.head.appendChild(styleElement);
    }
    
    // Call to add toast styles
    addToastStyles();

    // Handle contact form submission
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const subject = document.getElementById('subject')?.value || '';
            const message = document.getElementById('message').value;

            // In a real application, you would send this data to your server
            // Example: submitContactForm(name, email, subject, message);

            // For demo purposes, we'll show a toast notification
            createToast(`شكراً ${name} على رسالتك! سنتواصل معك قريباً على البريد الإلكتروني ${email}.`, 'success');

            // Clear the form
            this.reset();
        });
    }
    
    // Handle newsletter subscription
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            // In a real application, you would send this data to your server
            // Example: subscribeToNewsletter(email);
            
            // For demo purposes, we'll show a toast notification
            createToast(`تم تسجيلك في النشرة الإخبارية بنجاح! سيتم إرسال التحديثات إلى ${email}`, 'success');
            
            // Clear the form
            this.reset();
        });
    }

    // Helper function to get platform name in Arabic
    function getPlatformName(platform) {
        switch(platform) {
            case 'android':
                return 'أندرويد';
            case 'ios':
                return 'آيفون';
            case 'windows':
                return 'ويندوز';
            default:
                return platform;
        }
    }

    // Add animation to features on scroll
    const features = document.querySelectorAll('.feature');

    // Simple function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }

    // Function to handle scroll animation
    function handleScrollAnimation() {
        features.forEach(feature => {
            if (isInViewport(feature)) {
                feature.classList.add('animate');
            }
        });
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 70, // Adjust for header height
                    behavior: 'smooth'
                });
                
                // Add active class to the clicked nav link
                navLinks.forEach(navLink => {
                    navLink.classList.remove('active');
                });
                this.classList.add('active');
            }
        });
    });

    // Theme toggle functionality
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    if (themeToggleBtn) {
        const themeIcon = themeToggleBtn.querySelector('i');

        // Check for saved theme preference or use preferred color scheme
        const savedTheme = localStorage.getItem('theme');
        const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');

        if (savedTheme === 'dark' || (!savedTheme && prefersDarkScheme.matches)) {
            document.body.classList.add('dark-mode');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        }

        themeToggleBtn.addEventListener('click', function() {
            // Toggle dark mode class on body
            document.body.classList.toggle('dark-mode');

            // Toggle icon
            if (document.body.classList.contains('dark-mode')) {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'light');
            }
        });
    }
    
    // Initialize all components
    window.addEventListener('DOMContentLoaded', function() {
        // Initial check for scroll animations
        handleScrollAnimation();
        
        // Add scroll listener for animations
        window.addEventListener('scroll', handleScrollAnimation);
        
        // Initialize testimonials slider
        initTestimonialsSlider();
        
        // Initialize requirements tabs
        initRequirementsTabs();
        
        // Animate statistics counters
        animateStatisticCounters();
        
        // Update active nav link based on scroll position
        window.addEventListener('scroll', updateActiveNavLink);
        
        function updateActiveNavLink() {
            const scrollPosition = window.scrollY + 300;
            
            // Get all sections
            const sections = document.querySelectorAll('section[id]');
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                const sectionId = section.getAttribute('id');
                
                if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                    // Remove active class from all links
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                    });
                    
                    // Add active class to corresponding link
                    const activeLink = document.querySelector(`a[href="#${sectionId}"]`);
                    if (activeLink) {
                        activeLink.classList.add('active');
                    }
                }
            });
        }
        
        // Initial call to update active link
        updateActiveNavLink();
    });
});
